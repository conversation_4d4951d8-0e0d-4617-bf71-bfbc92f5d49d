<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="employee_rejection_wizard_form_view" model="ir.ui.view">
            <field name="name">employee.rejection.reason.wizard</field>
            <field name="model">employee.rejection.wizard</field>
            <field name="arch" type="xml">
            <!-- Rejection wizard -->
                <form>
                    <group>
                        <field name="rejection_reason" required="1"/>
                        <field name="employee_id" invisible="1"/>
                    </group>
                    <footer>
                        <button string="Confirm" name="button_confirm_reject" type="object" class="oe_highlight" data-hotkey="q"/>
                        <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>
    </data>
</odoo>
